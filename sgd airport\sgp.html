<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新加坡专业接送机服务</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script> <!-- @file sgp.html --><!-- @function html2canvas --><!-- 引入 html2canvas 库 -->
    <style>
        :root {
            --primary-blue: #0f172a;
            --secondary-blue: #1e40af;
            --accent-gold: #f59e0b;
            --dark-gold: #d97706;
            --light-gray: #f8fafc;
            --text-dark: #1e293b;
            --text-light: #64748b;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            color: var(--text-dark);
            line-height: 1.6;
        }

        /* 全局样式优化 */
        .premium-gradient {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 50%, #3b82f6 100%);
        }

        .gold-accent {
            background: linear-gradient(135deg, var(--accent-gold) 0%, var(--dark-gold) 100%);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .gold-text {
            background: linear-gradient(135deg, var(--accent-gold), var(--dark-gold));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-title {
            position: relative;
            color: var(--primary-blue);
            font-weight: 800;
            letter-spacing: -0.025em;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 60%;
            background: linear-gradient(135deg, var(--accent-gold), var(--dark-gold));
            border-radius: 3px;
            margin-right: 16px;
        }

        .section-title::after {
            content: '';
            position: absolute;
            left: 20px;
            bottom: -8px;
            width: 80px;
            height: 3px;
            background: linear-gradient(to right, var(--accent-gold), transparent);
            border-radius: 2px;
        }

        /* 卡片样式优化 */
        .premium-card {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            box-shadow:
                0 20px 25px -5px rgba(0, 0, 0, 0.1),
                0 10px 10px -5px rgba(0, 0, 0, 0.04),
                0 0 0 1px rgba(255, 255, 255, 0.05);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(229, 231, 235, 0.5);
            position: relative;
            overflow: hidden;
        }

        .premium-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--accent-gold), var(--dark-gold), var(--secondary-blue));
        }

        .premium-card:hover {
            transform: translateY(-12px);
            box-shadow:
                0 32px 64px -12px rgba(0, 0, 0, 0.15),
                0 25px 32px -10px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.1);
        }

        /* 标题渐变动画 */
        .animated-title {
            background: linear-gradient(
                45deg,
                #ffffff,
                var(--accent-gold),
                #ffffff,
                var(--accent-gold)
            );
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 4s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* 渐变遮罩优化 */
        .premium-overlay {
            background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 64, 175, 0.6) 50%, rgba(0, 0, 0, 0.4) 100%);
            pointer-events: none;
        }
        .premium-overlay * {
            pointer-events: auto;
        }

        /* 为可点击图片添加手型光标 */
        .hero-image {
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        .hero-image:hover {
            transform: scale(1.02);
        }

        .premium-overlay::before {
            content: '';
            position: absolute;
            inset: 0;
            background: radial-gradient(
                ellipse at center,
                transparent 0%,
                rgba(245, 158, 11, 0.1) 100%
            );
        }

        /* 按钮样式优化 */
        .premium-btn {
            background: linear-gradient(135deg, var(--secondary-blue), var(--primary-blue));
            color: white;
            border-radius: 16px;
            padding: 14px 32px;
            font-weight: 600;
            letter-spacing: 0.025em;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 10px 15px -3px rgba(30, 64, 175, 0.2),
                0 4px 6px -2px rgba(30, 64, 175, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .premium-btn::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, var(--accent-gold), var(--dark-gold));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .premium-btn:hover::before {
            opacity: 1;
        }

        .premium-btn:hover {
            transform: translateY(-2px);
            box-shadow:
                0 20px 25px -5px rgba(245, 158, 11, 0.3),
                0 10px 10px -5px rgba(245, 158, 11, 0.2);
        }

        .premium-btn span {
            position: relative;
            z-index: 2;
        }

        /* 图标圆圈优化 */
        .premium-icon {
            background: linear-gradient(135deg, var(--secondary-blue), var(--primary-blue));
            color: white;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 10px 15px -3px rgba(30, 64, 175, 0.2),
                0 4px 6px -2px rgba(30, 64, 175, 0.1);
        }

        .premium-icon::before {
            content: '';
            position: absolute;
            inset: -2px;
            background: linear-gradient(135deg, var(--accent-gold), var(--dark-gold));
            border-radius: 50%;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .premium-icon:hover::before {
            opacity: 1;
        }

        /* 装饰性元素 */
        .floating-element {
            position: absolute;
            background: linear-gradient(135deg, var(--accent-gold), var(--dark-gold));
            border-radius: 50%;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            width: 100px;
            height: 100px;
            top: 10%;
            right: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 5%;
            animation-delay: 2s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* 分隔线优化 */
        .premium-divider {
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--accent-gold), transparent);
            border-radius: 1px;
            margin: 3rem 0;
        }

        /* 响应式图片优化 */
        .hero-image {
            filter: brightness(0.9) contrast(1.1) saturate(1.2);
            transition: all 0.3s ease;
        }

        .card-image {
            border-radius: 16px 16px 0 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .premium-card:hover .card-image {
            transform: scale(1.05);
            filter: brightness(1.1) saturate(1.1);
        }

        /* 终端卡片特殊样式 */
        .terminal-premium-card {
            border-top: 6px solid;
            border-image: linear-gradient(90deg, var(--accent-gold), var(--dark-gold)) 1;
        }

        /* 列表样式优化 */
        .premium-list li {
            position: relative;
            padding-left: 2rem;
            margin-bottom: 1rem;
        }

        .premium-list li::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0.5rem;
            width: 8px;
            height: 8px;
            background: linear-gradient(135deg, var(--accent-gold), var(--dark-gold));
            border-radius: 50%;
        }

        /* 页脚优化 */
        .premium-footer {
            background: linear-gradient(135deg, var(--primary-blue) 0%, #1e40af 50%, #3730a3 100%);
            position: relative;
            overflow: hidden;
        }

        .premium-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--accent-gold), transparent);
        }

        /* 使所有图片自适应容器宽度，高度自动 */
        img {
            max-width: 100%;
            height: auto;
        }

        /* 滚动条优化 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--accent-gold), var(--dark-gold));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--dark-gold), var(--accent-gold));
        }

        /* 部分装饰 */
        .section-decoration {
            position: relative;
            overflow: hidden;
        }

        .section-decoration::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(
                circle,
                rgba(245, 158, 11, 0.03) 0%,
                transparent 70%
            );
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 行李尺寸对比 */
        .luggage-box {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            border: 1px solid #e0e7ff;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .luggage-box:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        /* 航站楼卡片优化 */
        .terminal-premium-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        .terminal-premium-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
        }

        .terminal-premium-card img {
            transition: transform 0.3s ease;
        }

        .terminal-premium-card:hover img {
            transform: scale(1.05);
        }

        /* 航站楼标题美化 */
        .terminal-premium-card h3 {
            position: relative;
            color: #1e3a8a;
            background: linear-gradient(45deg, #1e3a8a, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .terminal-premium-card h3::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(90deg, #D4AF37, #FFD700);
            border-radius: 2px;
        }

        /* 表格美化 */
        table {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        table thead tr {
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
        }

        table tbody tr:nth-child(even) {
            background: linear-gradient(135deg, rgba(248, 250, 252, 0.5), rgba(241, 245, 249, 0.5));
        }

        table tbody tr:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.1));
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        /* 入境须知卡片美化 */
        .immigration-notice-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            position: relative;
            padding: 2rem;
            overflow: hidden;
        }

        .immigration-notice-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }

        .immigration-notice-card .card-image {
            transition: all 0.4s ease;
        }

        .immigration-notice-card:hover .card-image {
            transform: scale(1.08);
            filter: brightness(1.1) saturate(1.2);
        }

        /* 入境须知卡片美化 */
        .immigration-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            position: relative;
            padding: 2rem;
            overflow: hidden;
        }

        .immigration-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        }

        .immigration-card .card-image {
            transition: all 0.4s ease;
            border-radius: 12px;
            overflow: hidden;
        }

        .immigration-card:hover .card-image {
            transform: scale(1.05);
        }

        /* 图标圆圈进一步美化 */
        .icon-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
        }

        .icon-circle:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .section-title {
                font-size: 2rem;
                padding-left: 1rem;
            }

            .terminal-premium-card {
                margin-bottom: 2rem;
            }

            .immigration-notice-card {
                margin-bottom: 2rem;
            }

            .icon-circle {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }
        }

        /* 产品图模式专用样式 */
        .product-mode {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .product-mode .premium-card {
            transform: scale(1.05);
            margin: 1.5rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .product-mode .section-title {
            font-size: 3rem;
            text-align: center;
            margin-bottom: 3rem;
        }

        .product-mode .animated-title {
            font-size: 5rem;
            line-height: 1.1;
        }

        /* 产品图导出优化 */
        @media print {
            .fixed {
                display: none !important;
            }

            body {
                background: white;
            }

            .premium-card {
                break-inside: avoid;
                margin-bottom: 2rem;
            }
        }

        /* ===================== 新版旅游路线与订前须知样式 ===================== */
    /* @section 旅游路线卡片与订前须知卡片新版样式 */
    .tour-section-gradient {
        background: linear-gradient(135deg, #e0e7ff 0%, #fdf6e3 100%);
        border-radius: 0;
        box-shadow: none;
        padding: 4rem 0;
        position: relative;
        isolation: isolate;
        overflow: hidden;
    }

    .tour-section-gradient::before {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(135deg, rgba(30,64,175,0.05), rgba(245,158,11,0.05));
        z-index: -1;
    }
    .tour-section-title {
        font-size: 2.5rem;
        font-weight: 900;
        background: linear-gradient(90deg, #1e40af 60%, #f59e0b 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 2rem;
        padding-left: 0.75rem;
        position: relative;
    }
    .tour-section-desc {
        font-size: 1.25rem;
        color: #64748b;
        margin-bottom: 2rem;
    }
    .tour-route-flex {
        display: flex;
        flex-wrap: wrap;
        gap: 2rem;
        justify-content: space-between;
    }
    .tour-route-card {
        background: rgba(255,255,255,0.96);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(30,64,175,0.08);
        overflow: hidden;
        flex: 1 1 320px;
        min-width: 320px;
        max-width: 420px;
        display: flex;
        flex-direction: column;
        transition: transform 0.3s cubic-bezier(.4,0,.2,1), box-shadow 0.3s;
        margin-bottom: 1rem;
    }
    .tour-route-card:hover {
        transform: translateY(-8px) scale(1.03);
        box-shadow: 0 16px 48px rgba(30,64,175,0.18);
    }
    .tour-card-img {
        width: 100%;
        height: 180px;
        object-fit: cover;
        border-radius: 20px 20px 0 0;
        transition: transform 0.3s, filter 0.3s;
    }
    .tour-route-card:hover .tour-card-img {
        transform: scale(1.05);
        filter: brightness(1.08) saturate(1.15);
    }
    .tour-card-content {
        padding: 1.5rem;
        display: flex;
        flex-direction: column;
        flex: 1;
    }
    .tour-card-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0.75rem;
    }
    .tour-card-list {
        padding-left: 1.5rem;
        color: #475569;
        margin-bottom: 1rem;
    }
    .tour-card-desc {
        color: #64748b;
        margin-top: auto;
        font-size: 1rem;
    }
    /* 响应式布局优化 */
    @media (max-width: 1024px) {
        .tour-route-flex {
            flex-direction: column;
            gap: 1.5rem;
        }
        .tour-route-card {
            max-width: 100%;
        }
    }

    /* ===================== 退改政策与订前须知新版样式 ===================== */
    .notice-section-gradient {
        background: linear-gradient(135deg, #fdf6e3 0%, #e0e7ff 100%);
        border-radius: 0;
        box-shadow: none;
        padding: 4rem 0;
        position: relative;
        isolation: isolate;
        overflow: hidden;
    }

    .notice-section-gradient::before {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(135deg, rgba(245,158,11,0.05), rgba(30,64,175,0.05));
        z-index: -1;
    }
    .notice-section-title {
        font-size: 2.5rem;
        font-weight: 900;
        background: linear-gradient(90deg, #f59e0b 60%, #1e40af 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 2rem;
        padding-left: 0.75rem;
        position: relative;
    }
    .notice-flex-wrap {
        display: flex;
        gap: 2rem;
        flex-wrap: wrap;
        justify-content: space-between;
    }
    .notice-policy-card {
        background: rgba(255,255,255,0.97);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(245,158,11,0.08);
        overflow: hidden;
        flex: 1 1 320px;
        min-width: 320px;
        max-width: 420px;
        display: flex;
        flex-direction: column;
        transition: transform 0.3s cubic-bezier(.4,0,.2,1), box-shadow 0.3s;
        margin-bottom: 1rem;
    }
    .notice-policy-card:hover {
        transform: translateY(-8px) scale(1.03);
        box-shadow: 0 16px 48px rgba(245,158,11,0.18);
    }
    .notice-card-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.5rem 1.5rem 0 1.5rem;
    }
    .notice-icon-bg {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: linear-gradient(135deg,#f59e0b,#1e40af);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.6rem;
        box-shadow: 0 4px 16px rgba(245,158,11,0.12);
    }
    .notice-card-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0;
    }
    .notice-policy-list {
        list-style: none;
        margin: 0;
        padding: 1.5rem;
        display: flex;
        flex-direction: column;
        gap: 1.2rem;
    }
    .notice-policy-item {
        display: flex;
        align-items: flex-start;
        gap: 0.8rem;
        font-size: 1.1rem;
        color: #475569;
        background: linear-gradient(90deg,rgba(245,158,11,0.05),rgba(30,64,175,0.05));
        border-radius: 12px;
        padding: 0.8rem 1rem;
        transition: background 0.2s;
    }
    .notice-policy-item i {
        font-size: 1.2rem;
        margin-top: 2px;
    }
    .notice-refund-all {
        background: linear-gradient(90deg,#d1fae5 0%,#f0fdf4 100%);
        color: #059669;
        font-weight: 600;
    }
    .notice-refund-half {
        background: linear-gradient(90deg,#fef9c3 0%,#fef3c7 100%);
        color: #ca8a04;
        font-weight: 600;
    }
    .notice-refund-none {
        background: linear-gradient(90deg,#fee2e2 0%,#fef2f2 100%);
        color: #dc2626;
        font-weight: 600;
    }
    /* 响应式布局优化 */
    @media (max-width: 1024px) {
        .notice-flex-wrap {
            flex-direction: column;
            gap: 1.5rem;
        }
        .notice-policy-card {
            max-width: 100%;
        }
    }
</style>
</head>
<body class="pb-16">
    <!-- 装饰性浮动元素 -->
    <div class="floating-element"></div>
    <div class="floating-element"></div>

    <!-- 顶部横幅 -->
    <header class="relative section-decoration">
        <div class="w-full h-screen lg:h-[70vh] overflow-hidden">
            <img src="https://c8.alamy.com/comp/2R6M2N2/aerial-view-of-marina-bay-sands-and-singapore-city-harbour-at-night-singapore-southeast-asia-asia-2R6M2N2.jpg" alt="新加坡滨海湾夜景" class="w-full h-full object-cover hero-image">
            <div class="absolute inset-0 premium-overlay flex flex-col justify-center items-center text-white px-6 text-center">
                <h1 class="text-5xl md:text-7xl font-black mb-4 tracking-wider animated-title leading-tight">
                    新加坡专业接送机服务
                </h1>
                <div class="w-32 h-2 gold-accent mb-8 rounded-full"></div>
                <p class="text-2xl md:text-3xl font-light opacity-95 max-w-4xl">
                    新加坡旅行一站式服务 · 品质之选 · 安全保障
                </p>
                <div class="mt-8 flex space-x-4">
                    <div class="px-6 py-3 glass-effect rounded-full text-lg font-semibold text-blue-900">
                        <i class="fas fa-star gold-text mr-2"></i>
                        专业品质
                    </div>
                    <div class="px-6 py-3 glass-effect rounded-full text-lg font-semibold text-blue-900">
                        <i class="fas fa-shield-alt gold-text mr-2"></i>
                        安全保障
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- @file sgp.html --><!-- @function exportPageAsImage --><!-- 导出页面为图片的按钮 -->
    <div class="fixed bottom-6 right-6 z-50">
        <button id="exportButton" class="premium-btn flex items-center shadow-2xl">
            <span class="flex items-center">
                <i class="fas fa-camera mr-3 text-lg"></i>
                导出为图片
            </span>
        </button>
        <button id="productViewButton" class="premium-btn flex items-center shadow-2xl ml-4" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
            <span class="flex items-center">
                <i class="fas fa-image mr-3 text-lg"></i>
                产品图模式
            </span>
        </button>
    </div>

    <main>
        <div class="premium-divider"></div>

        <!-- 车型选择 -->
        <section class="py-16 section-decoration">
            <h2 class="section-title text-4xl font-bold mb-12 pl-8">车型选择</h2>
            <p class="text-xl text-gray-600 mb-12 max-w-4xl leading-relaxed">
                根据您的需求和人数，我们提供多种车型选择，从经济型轿车到小型巴士，满足不同的接送需求。每款车型都经过精心维护，确保您的出行安全舒适。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                <!-- 标准轿车 -->
                <div class="premium-card">
                    <div class="h-64 overflow-hidden">
                        <!-- 更换为五座车的车内空间透视图，示例为丰田RAV4五座SUV内饰 -->
                        <img src="https://toyota-cms-media.s3.amazonaws.com/wp-content/uploads/2022/09/2023-toyota-rav4-hybrid-xse-interior-001.jpg" alt="五座SUV车内空间透视图" class="w-full h-full object-cover card-image">
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-slate-800 mb-4">标准轿车</h3>
                        <div class="flex justify-between mb-6">
                            <div class="flex items-center bg-blue-50 px-4 py-2 rounded-lg">
                                <i class="fas fa-user text-blue-700 mr-3 text-lg"></i>
                                <span class="font-semibold text-blue-700">4人</span>
                            </div>
                            <div class="flex items-center bg-amber-50 px-4 py-2 rounded-lg">
                                <i class="fas fa-suitcase text-amber-700 mr-3 text-lg"></i>
                                <span class="font-semibold text-amber-700">3大2中行李</span>
                            </div>
                        </div>
                        <p class="text-gray-600 mb-6 leading-relaxed">经济实惠的选择，适合小型家庭或商务出行，提供舒适的乘坐体验。</p>
                    </div>
                </div>

                <!-- 豪华轿车 -->
                <div class="premium-card">
                    <div class="h-64 overflow-hidden">
                        <img src="https://www.mbusa.com/content/dam/mb-nafta/us/myco/my25/e-class/e-sedan/gallery/series/gallery-class/2025-E-SEDAN-GAL-026-Q-WP.jpg" alt="奔驰E级内部" class="w-full h-full object-cover card-image">
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-slate-800 mb-4">豪华轿车 (奔驰E级同级)</h3>
                        <div class="flex justify-between mb-6">
                            <div class="flex items-center bg-blue-50 px-4 py-2 rounded-lg">
                                <i class="fas fa-user text-blue-700 mr-3 text-lg"></i>
                                <span class="font-semibold text-blue-700">4人</span>
                            </div>
                            <div class="flex items-center bg-amber-50 px-4 py-2 rounded-lg">
                                <i class="fas fa-suitcase text-amber-700 mr-3 text-lg"></i>
                                <span class="font-semibold text-amber-700">3大3中行李</span>
                            </div>
                        </div>
                        <p class="text-gray-600 mb-6 leading-relaxed">高级商务之选，配备真皮座椅和豪华内饰，为您提供尊贵舒适的乘坐体验。</p>
                    </div>
                </div>

                <!-- 商务MPV -->
                <div class="premium-card">
                    <div class="h-64 overflow-hidden">
                        <img src="https://www.mundilimos.com/wp-content/uploads/2021/06/Sprinter-Van-Interior-05.jpg" alt="商务MPV内部" class="w-full h-full object-cover card-image">
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-slate-800 mb-4">商务MPV (丰田 Voxy)</h3>
                        <div class="flex justify-between mb-6">
                            <div class="flex items-center bg-blue-50 px-4 py-2 rounded-lg">
                                <i class="fas fa-user text-blue-700 mr-3 text-lg"></i>
                                <span class="font-semibold text-blue-700">4人</span>
                            </div>
                            <div class="flex items-center bg-amber-50 px-4 py-2 rounded-lg">
                                <i class="fas fa-suitcase text-amber-700 mr-3 text-lg"></i>
                                <span class="font-semibold text-amber-700">4大4中行李</span>
                            </div>
                        </div>
                        <p class="text-gray-600 mb-6 leading-relaxed">家人或朋友出行，宽敞舒适，拥有充足的乘坐空间和行李存放空间。</p>
                    </div>
                </div>

                <!-- 丰田阿尔法 -->
                <div class="premium-card">
                    <div class="h-64 overflow-hidden">
                        <img src="https://www.batfa.com/photo-newcar-alphardhybrid-interior.files/Alphard-interior-Flaxen-8.jpg" alt="丰田阿尔法内部" class="w-full h-full object-cover card-image">
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-slate-800 mb-4">丰田阿尔法/威尔法</h3>
                        <div class="flex justify-between mb-6">
                            <div class="flex items-center bg-blue-50 px-4 py-2 rounded-lg">
                                <i class="fas fa-user text-blue-700 mr-3 text-lg"></i>
                                <span class="font-semibold text-blue-700">5人</span>
                            </div>
                            <div class="flex items-center bg-amber-50 px-4 py-2 rounded-lg">
                                <i class="fas fa-suitcase text-amber-700 mr-3 text-lg"></i>
                                <span class="font-semibold text-amber-700">5大3中行李</span>
                            </div>
                        </div>
                        <p class="text-gray-600 mb-6 leading-relaxed">旗舰级豪华MPV，大空间配备顶级内饰，为团体旅行提供舒适享受。</p>
                    </div>
                </div>

                <!-- 小巴 -->
                <div class="premium-card">
                    <div class="h-64 overflow-hidden">
                        <img src="https://www.chicagovanrentals.com/userdata/vehicle/20240412_052643_704381.jpg" alt="小巴内部" class="w-full h-full object-cover card-image">
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-slate-800 mb-4">小巴</h3>
                        <div class="flex justify-between mb-6">
                            <div class="flex items-center bg-blue-50 px-4 py-2 rounded-lg">
                                <i class="fas fa-user text-blue-700 mr-3 text-lg"></i>
                                <span class="font-semibold text-blue-700">10人</span>
                            </div>
                            <div class="flex items-center bg-amber-50 px-4 py-2 rounded-lg">
                                <i class="fas fa-suitcase text-amber-700 mr-3 text-lg"></i>
                                <span class="font-semibold text-amber-700">6大6中行李</span>
                            </div>
                        </div>
                        <p class="text-gray-600 mb-6 leading-relaxed">中型团体旅行首选，多人共乘经济实惠，舒适空调和专业司机服务。</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 行李规格参考 -->
        <section class="py-16 bg-gray-50 rounded-lg">
            <h2 class="section-title text-4xl font-bold mb-12 pl-8">行李规格详细参考</h2>

            <div class="mb-12">
                <h3 class="text-2xl font-semibold text-blue-800 mb-6">行李尺寸对比</h3>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex flex-col lg:flex-row justify-between items-center mb-8">
                        <img src="https://www.youcouldtravel.com/u/s/standard-suitcase-sizes-comparison-chart.jpg" alt="行李尺寸对比" class="w-full lg:w-1/2 rounded-lg mb-6 lg:mb-0">

                        <div class="w-full lg:w-1/2 lg:pl-8">
                            <table class="w-full border-collapse">
                                <thead>
                                    <tr class="bg-blue-900 text-white">
                                        <th class="border p-3 text-left">行李类型</th>
                                        <th class="border p-3 text-left">尺寸范围</th>
                                        <th class="border p-3 text-left">重量范围</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="border p-3 font-medium">大型行李<br>(28-32英寸)</td>
                                        <td class="border p-3">75-82厘米高</td>
                                        <td class="border p-3">20-32公斤</td>
                                    </tr>
                                    <tr class="bg-gray-50">
                                        <td class="border p-3 font-medium">中型行李<br>(24-27英寸)</td>
                                        <td class="border p-3">60-70厘米高</td>
                                        <td class="border p-3">15-25公斤</td>
                                    </tr>
                                    <tr>
                                        <td class="border p-3 font-medium">小型行李<br>(20-22英寸)</td>
                                        <td class="border p-3">50-55厘米高</td>
                                        <td class="border p-3">7-10公斤</td>
                                    </tr>
                                    <tr class="bg-gray-50">
                                        <td class="border p-3 font-medium">随身小包<br>(背包/手提包)</td>
                                        <td class="border p-3">≤40×30×20厘米</td>
                                        <td class="border p-3">通常≤7公斤</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                        <div class="luggage-box p-4 bg-blue-50">
                            <h4 class="font-bold text-blue-900 mb-2">行李容量参考</h4>
                            <ul class="list-disc pl-5 text-gray-700">
                                <li>大型行李：可装约3-4周的换洗衣物</li>
                                <li>中型行李：适合7-10天的旅行</li>
                                <li>小型行李：适合周末短途旅行</li>
                            </ul>
                        </div>

                        <div class="luggage-box p-4 bg-blue-50">
                            <h4 class="font-bold text-blue-900 mb-2">航空行李重量限制</h4>
                            <ul class="list-disc pl-5 text-gray-700">
                                <li>经济舱：通常23公斤/件</li>
                                <li>商务/头等舱：通常32公斤/件</li>
                                <li>尺寸总和不超过158厘米(长+宽+高)</li>
                            </ul>
                        </div>

                        <div class="luggage-box p-4 bg-blue-50">
                            <h4 class="font-bold text-blue-900 mb-2">特殊行李处理</h4>
                            <ul class="list-disc pl-5 text-gray-700">
                                <li>高尔夫球杆：可能需额外费用 （下单前请咨询客服）</li>
                                <li>儿童推车：只能接受能折叠型号</li>
                                <li>自行车：需提前预订特殊车型</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

        <!-- 机场航站楼上车点 -->
        <section class="py-16">
            <h2 class="section-title text-4xl font-bold mb-12 pl-8">樟宜机场航站楼上车点</h2>
            <p class="text-xl text-gray-600 mb-8 max-w-4xl leading-relaxed">新加坡樟宜机场各航站楼指定上车点位置，我们的司机将在此处等候接机。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- T1航站楼 -->
                <div class="terminal-premium-card bg-white rounded-lg overflow-hidden shadow-md">
                    <div class="h-64 overflow-hidden">
                        <!--
                            调整图片显示比例为16:9，使用相对定位和padding-top实现响应式16:9比例盒子，
                            并让图片绝对定位填充盒子，object-fit保持裁剪效果。
                        -->
                        <div class="relative w-full" style="padding-top: 56.25%;"> <!-- 16:9比例盒子 -->
                            <img src="https://cpgconsultants.com.sg/wp-content/uploads/2022/03/Night-View-Changi-Airport-Terminal-3-Singapore-Large.jpg"
                                 alt="樟宜机场T1航站楼"
                                 class="absolute top-0 left-0 w-full h-full object-cover rounded-lg">
                        </div>
                    </div>
                    <!--
                        T1航站楼上车点详细指引，参考英文原图内容，分步说明并配图。
                        每一步骤配合图片和详细描述，帮助用户顺利找到上车点。
                    -->
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-blue-900 mb-3">T1航站楼上车点（详细指引）</h3>
                        <ol class="list-decimal pl-5 text-gray-700 space-y-4 mb-4">
                            <li>
                                <div class="mb-2">
                                    <span class="font-semibold text-blue-800">抵达大厅：</span>
                                    <span>行李提取后，寻找黄色指示牌"Ride-Hailing & Arrival Pick-Up / Taxi"。前往"Jewel Lobby B"旁的自动扶梯。</span>
                                </div>
                                <!--
                                    调整图片显示比例为16:9，使用相对定位和padding-top实现响应式16:9比例盒子，
                                    并让图片绝对定位填充盒子，object-fit保持裁剪效果。
                                -->
                                <div class="relative w-full" style="padding-top: 56.25%;"> <!-- 16:9比例盒子 -->
                                    <img src="https://i.imgur.com/0Qw6QwF.jpg" alt="T1大厅指示牌" class="absolute top-0 left-0 w-full h-full object-cover rounded mb-2">
                                </div>
                            </li>
                            <li>
                                <div class="mb-2">
                                    <span class="font-semibold text-blue-800">乘坐下行扶梯：</span>
                                    <span>根据"Ride-Hailing & Arrival Pick-Up (Doors 10-12)"的标识，乘坐下行扶梯前往地下一层。</span>
                                </div>
                                <div class="relative w-full" style="padding-top: 56.25%;"> <!-- 16:9比例盒子 -->
                                    <img src="https://i.imgur.com/8Qw6QwF.jpg" alt="T1下行扶梯" class="absolute top-0 left-0 w-full h-full object-cover rounded mb-2">
                                </div>
                            </li>
                            <li>
                                <div class="mb-2">
                                    <span class="font-semibold text-blue-800">到达地下一层：</span>
                                    <span>下扶梯后，直行并根据天花板和路边的指示牌，前往"Doors 10-12"方向。</span>
                                </div>
                                <div class="relative w-full" style="padding-top: 56.25%;"> <!-- 16:9比例盒子 -->
                                    <img src="https://i.imgur.com/9Qw6QwF.jpg" alt="T1地下一层指示" class="absolute top-0 left-0 w-full h-full object-cover rounded mb-2">
                                </div>
                            </li>
                            <li>
                                <div class="mb-2">
                                    <span class="font-semibold text-blue-800">到达上车区：</span>
                                    <span>抵达10-12号门，即为专属网约车/接送机上车点，请在此等候司机。</span>
                                </div>
                                <div class="relative w-full" style="padding-top: 56.25%;"> <!-- 16:9比例盒子 -->
                                    <img src="https://i.imgur.com/7Qw6QwF.jpg" alt="T1上车区" class="absolute top-0 left-0 w-full h-full object-cover rounded mb-2">
                                </div>
                            </li>
                        </ol>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <p class="font-semibold text-blue-800">指定上车区域:</p>
                            <ul class="list-disc pl-5 text-gray-700">
                                <li>10-12号门：地下一层（B1），请根据现场黄色指示牌前往</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- T2航站楼 -->
                <div class="terminal-premium-card bg-white rounded-lg overflow-hidden shadow-md">
                    <div class="h-64 overflow-hidden">
                        <img src="https://interiordesign.net/wp-content/uploads/2024/10/idx241001_boifillsarchitect08_2.jpg" alt="樟宜机场T2航站楼" class="w-full h-full object-cover">
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-blue-900 mb-3">T2航站楼上车点</h3>
                        <p class="text-gray-600 mb-4 leading-relaxed">抵达迎宾大厅中央（靠近咨询柜台），然后前往指定上车区域。</p>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <p class="font-semibold text-blue-800">指定上车区域:</p>
                            <ul class="list-disc pl-5 text-gray-700">
                                <li>3号门：位于航站楼中央区域</li>
                                <li>遵循地面标识前往私人车辆等候区</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- T3航站楼 -->
                <div class="terminal-premium-card bg-white rounded-lg overflow-hidden shadow-md">
                    <div class="h-64 overflow-hidden">
                        <img src="https://cpgconsultants.com.sg/wp-content/uploads/2022/03/View-down-to-Departure-Transit-Mall-Changi-Airport-Terminal-3-Singapore.jpg" alt="樟宜机场T3航站楼" class="w-full h-full object-cover">
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-blue-900 mb-3">T3航站楼上车点</h3>
                        <!-- 步骤说明，参考英文原图内容，详细分步 -->
                        <ol class="list-decimal pl-5 text-gray-700 space-y-2 mb-4">
                            <li>
                                按照机场内"Ride-Hailing & Arrival Pick-Up"指示牌前往。可在樟宜机场推荐服务台和地面交通服务台附近找到相关标识，乘坐扶梯或电梯下至地下一层（B1）。
                            </li>
                            <li>
                                到达B1后，跟随黄色指示牌，前往"Ride-Hailing & Arrival Pick-Up 1号门和2号门"。
                            </li>
                            <li>
                                在B1层的1号门或2号门等候司机接送，请保持与司机联系。
                            </li>
                        </ol>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <p class="font-semibold text-blue-800">指定上车区域:</p>
                            <ul class="list-disc pl-5 text-gray-700">
                                <li>1号门、2号门：位于地下一层（B1）</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- T4航站楼 -->
                <div class="terminal-premium-card bg-white rounded-lg overflow-hidden shadow-md">
                    <div class="h-64 overflow-hidden">
                        <img src="https://interiordesign.net/wp-content/uploads/2024/10/idx241001_boifillsarchitect10.jpg" alt="樟宜机场T4航站楼" class="w-full h-full object-cover">
                    </div>
                    <!-- T4航站楼上车点详细指引，结合英文原图内容，分步说明 -->
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-blue-900 mb-3">T4航站楼上车点</h3>
                        <!-- 步骤说明，参考英文原图内容，详细分步 -->
                        <ol class="list-decimal pl-5 text-gray-700 space-y-2 mb-4">
                            <li>
                                从到达大厅出发，留意头顶的黄色指示牌，按照"Ride-Hailing & Arrival Pick-up（1-3号门）"的方向前进。
                            </li>
                            <li>
                                沿途会经过旅游信息柜台和座位区，继续沿走廊步行，前往5号门及更远的区域。
                            </li>
                            <li>
                                按照"Ride-Hailing Pick-up"标识，最终在5-7号门等候司机接送。
                            </li>
                        </ol>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <p class="font-semibold text-blue-800">指定上车区域:</p>
                            <ul class="list-disc pl-5 text-gray-700">
                                <li>5-7号门：请在此区域等候司机</li>
                            </ul>
                        </div>
                        <!-- 步骤配图，帮助旅客理解路线 -->
                        <img src="https://user-images.githubusercontent.com/your-image-path/t4-pickup-guide.jpg" alt="T4航站楼上车点指引" class="w-full aspect-video object-cover rounded-lg mt-4">
                        <!--
                            上述图片请替换为实际可用的图片链接。
                            如需本地上传，可点击图片进行替换。
                        -->
                    </div>
                </div>
            </div>
        </section>

        <!-- 新加坡入境须知 -->
        <section class="py-16 bg-gray-50 rounded-lg">
            <h2 class="section-title text-4xl font-bold mb-12 pl-8">新加坡入境须知</h2>
            <p class="text-xl text-gray-600 mb-8 max-w-4xl leading-relaxed">了解新加坡入境的重要信息，确保您的旅行顺利进行。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- 电子入境卡填写指南（针对中国旅客） -->
                <div class="immigration-card bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="icon-circle mr-4">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-blue-900">电子入境卡填写指南</h3>
                    </div>
                    <!-- 中文注释：简要说明电子入境卡的作用和填写时间 -->
                    <p class="text-gray-600 mb-2 leading-relaxed">
                        <span class="font-bold">所有中国旅客</span>入境新加坡前，<span class="text-blue-800 font-semibold">必须提前在线填写电子入境卡（SG Arrival Card）</span>，最早可在入境前3天内提交。
                    </p>
                    <!-- 中文注释：实际填写网址 -->
                    <p class="text-gray-600 mb-2 leading-relaxed">
                        官方填写网址：<a href="https://eservices.ica.gov.sg/sgarrivalcard/" target="_blank" class="text-blue-700 underline">https://eservices.ica.gov.sg/sgarrivalcard/</a>
                        <br>
                        也可下载"MyICA"手机APP填写。
                    </p>
                    <!-- 中文注释：填写内容 -->
                    <p class="text-gray-600 mb-2 leading-relaxed">
                        <span class="font-bold">填写内容：</span>个人信息（姓名、护照号）、航班信息、在新加坡的住址、健康申报等。
                    </p>
                    <!-- 中文注释：填写步骤 -->
                    <ol class="list-decimal pl-5 text-gray-700 mb-2">
                        <li>打开上方网址或APP，选择"Foreign Visitor（外国访客）"</li>
                        <li>按页面提示填写个人资料、旅行信息和健康申报</li>
                        <li>核对信息无误后提交，系统会显示确认页面</li>
                        <li>建议截图或保存确认页面，入境时备查</li>
                    </ol>
                    <!-- 中文注释：注意事项 -->
                    <p class="text-gray-600 leading-relaxed">
                        <span class="font-bold text-red-600">注意：</span>每人都需单独填写，包括儿童。未填写电子入境卡将无法顺利入境。
                    </p>
                </div>

                <!-- 中国护照免签与签证须知 -->
                <div class="immigration-card bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="icon-circle mr-4">
                            <i class="fas fa-passport"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-blue-900">中国护照免签与签证须知</h3>
                    </div>
                    <!-- 中文注释：免签政策 -->
                    <p class="text-gray-600 mb-2 leading-relaxed">
                        <span class="font-bold">自2024年2月9日起</span>，中国护照持有人可<span class="text-blue-800 font-semibold">免签入境新加坡</span>，最多可停留30天。
                    </p>
                    <!-- 中文注释：护照有效期要求 -->
                    <p class="text-gray-600 mb-2 leading-relaxed">
                        <span class="font-bold">护照要求：</span>入境时护照有效期需<span class="text-red-600 font-semibold">至少6个月</span>以上。
                    </p>
                    <!-- 中文注释：超期与签证说明 -->
                    <p class="text-gray-600 mb-2 leading-relaxed">
                        <span class="font-bold">如需停留超过30天</span>，请提前在新加坡驻华使领馆或移民局官网申请签证。
                    </p>
                    <!-- 中文注释：入境材料提示 -->
                    <p class="text-gray-600 leading-relaxed">
                        <span class="font-bold">入境时请准备：</span>有效护照、已填写的电子入境卡、返程机票、在新加坡的住宿信息等。
                    </p>
                </div>
                <!-- 禁止携带物品（简明易懂说明） -->
                <div class="immigration-card bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="icon-circle mr-4">
                            <i class="fas fa-ban"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-blue-900">禁止携带物品</h3>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg mb-4">
                        <!-- 中文注释：用简单语言列出常见违禁品 -->
                        <ul class="list-disc pl-5 text-gray-700">
                            <li class="mb-2"><span class="font-medium">口香糖：</span>新加坡不允许带口香糖入境，只有医生开的药用口香糖才可以。</li>
                            <li class="mb-2"><span class="font-medium">烟草制品：</span>不能带电子烟、加热烟、普通烟草等。</li>
                            <li class="mb-2"><span class="font-medium">毒品和特殊药物：</span>任何毒品和没有许可的药物都不能带。</li>
                            <li><span class="font-medium">其他危险物品：</span>比如鞭炮、烟花、仿真枪等也不能带。</li>
                        </ul>
                    </div>
                    <p class="text-gray-600 leading-relaxed">
                        <!-- 中文注释：简化警示语 -->
                        如果带了这些违禁品，可能会被重罚甚至坐牢。请大家一定要注意，不要带这些东西入境新加坡。
                    </p>
                </div>

                <!-- 生物识别通关（简明易懂说明） -->
                <div class="immigration-card bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="icon-circle mr-4">
                            <i class="fas fa-fingerprint"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-blue-900">生物识别通关</h3>
                    </div>
                    <!-- 中文注释：用通俗语言解释流程 -->
                    <p class="text-gray-600 mb-2 leading-relaxed">
                        入境新加坡时，工作人员会采集您的虹膜、面部和指纹信息。这是为了让大家通关更快更安全。
                    </p>
                    <p class="text-gray-600 leading-relaxed">
                        如果您之前已经登记过这些信息，下次入境可以直接用自助通道，非常方便。
                    </p>
                </div>

        <!-- 热门旅游路线新版布局 -->
    <section class="tour-section-gradient py-16 w-full max-w-[1920px] mx-auto px-6 lg:px-8">
            <!-- 中文注释：新版旅游路线标题 -->
            <h2 class="tour-section-title">新加坡热门旅游路线</h2>
            <p class="tour-section-desc">提供包车服务，带您探索新加坡的经典景点和隐藏宝藏。</p>
            <div class="tour-route-flex">
                <!-- 市区经典路线卡片 -->
                <div class="tour-route-card">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/b/b2/1_marina_bay_sands_skypark_night_view_CBD_skyline.jpg/2560px-1_marina_bay_sands_skypark_night_view_CBD_skyline.jpg" alt="市区经典路线" class="tour-card-img">
                    <div class="tour-card-content">
                        <h3 class="tour-card-title">市区经典路线</h3>
                        <ul class="tour-card-list">
                            <li>鱼尾狮公园 - 新加坡国家象征</li>
                            <li>滨海湾花园 - 未来主义植物园</li>
                            <li>牛车水 - 传统中华文化区</li>
                            <li>金沙空中花园 - 壮观城市全景</li>
                        </ul>
                        <p class="tour-card-desc">时长：约6小时，适合首次访问新加坡的旅客，体验狮城精华景点。</p>
                    </div>
                </div>
                <!-- 文化探索路线卡片 -->
                <div class="tour-route-card">
                    <img src="https://cpgconsultants.com.sg/wp-content/uploads/2022/03/Night-View-Changi-Airport-Terminal-3-Singapore-Large.jpg" alt="文化探索路线" class="tour-card-img">
                    <div class="tour-card-content">
                        <h3 class="tour-card-title">文化探索路线</h3>
                        <ul class="tour-card-list">
                            <li>新加坡国家博物馆 - 历史与文化展览</li>
                            <li>国家美术馆 - 东南亚艺术珍藏</li>
                            <li>小印度 - 丰富的印度文化体验</li>
                            <li>甘榜格南 - 马来文化区域</li>
                        </ul>
                        <p class="tour-card-desc">时长：约5小时，深入了解新加坡多元文化，适合文化爱好者。</p>
                    </div>
                </div>
                <!-- 家庭休闲路线卡片 -->
                <div class="tour-route-card">
                    <img src="https://i.ytimg.com/vi/WunLZMPQDnE/maxresdefault.jpg" alt="家庭休闲路线" class="tour-card-img">
                    <div class="tour-card-content">
                        <h3 class="tour-card-title">家庭休闲路线</h3>
                        <ul class="tour-card-list">
                            <li>环球影城 - 世界级主题公园</li>
                            <li>S.E.A海洋馆 - 超过100,000种海洋生物</li>
                            <li>新加坡动物园 - 开放式自然栖息地</li>
                            <li>夜间野生动物园 - 独特夜间动物体验</li>
                        </ul>
                        <p class="tour-card-desc">时长：约8小时，适合家庭旅行，孩子们会喜欢这些互动性强的景点。</p>
                    </div>
                </div>
                <!-- 购物天堂路线卡片 -->
                <div class="tour-route-card">
                    <img src="https://interiordesign.net/wp-content/uploads/2024/10/idx241001_boifillsarchitect08_2.jpg" alt="购物天堂路线" class="tour-card-img">
                    <div class="tour-card-content">
                        <h3 class="tour-card-title">购物天堂路线</h3>
                        <ul class="tour-card-list">
                            <li>乌节路 - 亚洲顶级购物街</li>
                            <li>ION Orchard - 豪华购物中心</li>
                            <li>滨海湾金沙购物中心 - 顶级品牌集中地</li>
                            <li>武吉士 - 年轻人潮流购物区</li>
                        </ul>
                        <p class="tour-card-desc">时长：约7小时，新加坡购物体验，从高端奢侈品到本地特色商品一应俱全。</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 退改政策与须知新版布局 -->
    <section class="notice-section-gradient py-16 w-full max-w-[1920px] mx-auto px-6 lg:px-8">
            <!-- 中文注释：新版退改政策与订前须知标题 -->
            <h2 class="notice-section-title">退改政策与订前须知</h2>
            <div class="notice-flex-wrap">
                <!-- 退改政策卡片 -->
                <div class="notice-policy-card">
                    <div class="notice-card-header">
                        <span class="notice-icon-bg"><i class="fas fa-undo-alt"></i></span>
                        <h3 class="notice-card-title">退改政策</h3>
                    </div>
                    <ul class="notice-policy-list">
                        <li class="notice-policy-item notice-refund-all">
                            <i class="fas fa-check-circle"></i>
                            <span>3天以上取消：全额退款</span>
                        </li>
                        <li class="notice-policy-item notice-refund-half">
                            <i class="fas fa-exclamation-circle"></i>
                            <span>2天以上取消：50%退款</span>
                        </li>
                        <li class="notice-policy-item notice-refund-none">
                            <i class="fas fa-times-circle"></i>
                            <span>1天及以内取消：不予退款</span>
                        </li>
                    </ul>
                </div>
                <!-- 订前须知卡片 -->
                <div class="notice-policy-card">
                    <div class="notice-card-header">
                        <span class="notice-icon-bg"><i class="fas fa-info-circle"></i></span>
                        <h3 class="notice-card-title">订前须知</h3>
                    </div>
                    <ul class="notice-policy-list">
                        <li class="notice-policy-item">
                            <i class="fas fa-clock"></i>
                            <span>航班延误：如遇航班延误，请务必提前通知。司机将在航班落地后免费等待60分钟，超时将收取等候费用，具体费用根据所选车型而定。</span>
                        </li>
                        <li class="notice-policy-item">
                            <i class="fas fa-baby-carriage"></i>
                            <span>儿童安全座椅：可提供儿童安全座椅，需在预订时提前说明，会收取额外费用。</span>
                        </li>
                        <li class="notice-policy-item">
                            <i class="fas fa-suitcase-rolling"></i>
                            <span>行李额外收费：超出车型限定的行李数量，需要支付额外费用或升级车型。如抵达后司机发现与预订时说明不符，司机有权拒载，且无法退款。</span>
                        </li>
                        <li class="notice-policy-item">
                            <i class="fas fa-headset"></i>
                            <span>24小时客服支持：全天候客服团队为您提供服务，解决旅途中的任何问题。</span>
                        </li>
                    </ul>
                </div>
            </div>
        </section>
    <!-- @file sgp.html --><!-- @function exportPageAsImage --><!-- 导出页面为图片的脚本 -->
    <script>
        // @function handleExportClick
        // 处理导出按钮点击事件
        document.getElementById('exportButton').addEventListener('click', function() {
            console.log('用户点击了导出为图片按钮'); // 添加日志

            const exportButton = this; // 获取按钮元素
            exportButton.style.display = 'none'; // 隐藏按钮
            console.log('隐藏导出按钮'); // 添加日志

            // 使用 html2canvas 捕获整个页面
            html2canvas(document.body, {
                scale: 2, // 增加缩放比例以提高清晰度
                width: document.body.offsetWidth, // 设置宽度为页面实际宽度
                height: document.body.offsetHeight // 设置高度为页面实际高度
            }).then(function(canvas) {
                console.log('html2canvas 成功生成 canvas'); // 添加日志

                // 截图完成后显示按钮
                exportButton.style.display = ''; // 恢复按钮显示
                console.log('显示导出按钮'); // 添加日志

                var link = document.createElement('a');
                link.download = '新加坡专业接送机服务-页面截图.png'; // 设置下载文件名
                link.href = canvas.toDataURL('image/png'); // 将 canvas 转换为图片数据
                document.body.appendChild(link); // 将链接添加到文档中
                link.click(); // 模拟点击下载
                document.body.removeChild(link); // 下载后移除链接
                console.log('图片下载链接已创建并尝试点击'); // 添加日志
            }).catch(function(error) {
                console.error('导出页面为图片失败:', error); // 使用 console.error 记录错误

                // 发生错误时也要确保按钮显示
                exportButton.style.display = ''; // 恢复按钮显示
                console.log('导出失败，显示导出按钮'); // 添加日志

                alert('导出图片失败，请查看控制台获取更多信息。'); // 提示用户导出失败
            });
        });
    </script>

    <!-- 以下脚本为所有 <img> 元素添加点击上传本地图片替换功能 -->
    <script>
        console.log('脚本：开始为所有图片添加点击上传功能'); // 添加日志
        // 遍历页面中所有图片
        document.querySelectorAll('img').forEach((img, index) => {
            console.log('处理图片:', img.alt || img.src, '索引:', index); // 添加日志
            // 创建隐藏的文件上传输入框
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.style.display = 'none';
            img.parentNode.insertBefore(input, img.nextSibling);
            // 设置鼠标样式，提示可点击上传
            img.style.cursor = 'pointer';
            // 点击图片时触发文件选择对话框
            img.addEventListener('click', () => {
                console.log('图片被点击，触发文件选择', img.alt || img.src); // 添加日志
                input.click();
            });
            // 如果父容器中存在渐变遮罩（.gradient-overlay），点击遮罩也触发上传
            const parent = img.parentNode;
            const overlay = parent.querySelector('.gradient-overlay');
            if (overlay) {
                overlay.style.cursor = 'pointer';
                overlay.addEventListener('click', (e) => {
                    // 保留遮罩中按钮等交互，不触发上传
                    if (e.target.closest('button') || e.target.closest('a')) return;
                    console.log('渐变遮罩被点击，触发文件选择'); // 添加日志
                    input.click();
                });
            }
            // 选择文件后，用 FileReader 读取并替换图片 src
            input.addEventListener('change', (e) => {
                console.log('文件选择器 change 事件触发'); // 添加日志

                // 截图完成后显示按钮
                const file = e.target.files[0];
                if (!file) {
                    console.log('未选择文件或文件选择已取消'); // 添加日志
                    return;
                }
                console.log('已选择文件:', file.name, '大小:', file.size); // 添加日志
                const reader = new FileReader();

                // FileReader 加载成功事件
                reader.onload = (event) => {
                    console.log('FileReader 加载成功，准备替换图片 src'); // 添加日志
                    img.src = event.target.result; // 用本地文件的 DataURL 替换图片
                    console.log('图片 src 已更新'); // 添加日志
                };

                // FileReader 加载失败事件
                reader.onerror = (error) => {
                    console.error('FileReader 加载文件时发生错误:', error); // 记录错误
                    alert('读取文件失败，请重试或检查文件。'); // 提示用户
                };

                reader.readAsDataURL(file); // 读取文件为 DataURL
                console.log('FileReader 开始读取文件'); // 添加日志
            });
        });
        console.log('脚本：已为所有图片添加点击上传功能'); // 添加日志

        // 产品图模式功能
        document.getElementById('productViewButton').addEventListener('click', function() {
            // 隐藏不必要的部分
            const sectionsToHide = [
                'section:nth-of-type(n+3)', // 隐藏第3个section及之后的所有section
                '.premium-divider',
                'footer'
            ];

            sectionsToHide.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    el.style.display = 'none';
                });
            });

            // 优化车型展示区域
            const vehicleSection = document.querySelector('section:nth-of-type(2)');
            if (vehicleSection) {
                // 只显示前4个车型卡片
                const cards = vehicleSection.querySelectorAll('.premium-card');
                cards.forEach((card, index) => {
                    if (index >= 4) {
                        card.style.display = 'none';
                    } else {
                        // 增大字体和间距
                        const title = card.querySelector('h3');
                        const description = card.querySelector('p');
                        if (title) title.style.fontSize = '1.8rem';
                        if (description) description.style.fontSize = '1.2rem';
                        card.style.marginBottom = '2rem';
                    }
                });

                // 调整网格布局为2x2
                const grid = vehicleSection.querySelector('.grid');
                if (grid) {
                    grid.className = 'grid grid-cols-2 gap-8';
                }
            }

            // 优化标题区域
            const header = document.querySelector('header');
            if (header) {
                header.style.height = '60vh'; // 减少高度
                const title = header.querySelector('h1');
                if (title) {
                    title.style.fontSize = '4rem';
                    title.style.marginBottom = '2rem';
                }
            }

            // 隐藏导出按钮
            this.style.display = 'none';

            // 添加恢复按钮
            const restoreButton = document.createElement('button');
            restoreButton.innerHTML = '<span class="flex items-center"><i class="fas fa-undo mr-3 text-lg"></i>恢复完整版</span>';
            restoreButton.className = 'premium-btn flex items-center shadow-2xl';
            restoreButton.style.background = 'linear-gradient(135deg, #10b981, #059669)';
            restoreButton.onclick = () => location.reload();
            this.parentNode.appendChild(restoreButton);

            alert('已切换到产品图模式！页面已优化为适合导出的格式。');
        });
    </script>
</body>
</html>
