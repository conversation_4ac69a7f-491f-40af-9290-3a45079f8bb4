<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新加坡专业接送机服务 - 产品宣传图</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        :root {
            --primary-blue: #0f172a;
            --secondary-blue: #1e40af;
            --accent-gold: #f59e0b;
            --dark-gold: #d97706;
            --light-gray: #f8fafc;
            --text-dark: #1e293b;
            --text-light: #64748b;
        }

        /* 垂直长图专用样式 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(180deg, #f1f5f9 0%, #e2e8f0 50%, #f8fafc 100%);
            color: var(--text-dark);
            line-height: 1.6;
            margin: 0;
            padding: 0;
            width: 1200px;
            margin: 0 auto;
            min-height: 4000px;
        }

        /* 主容器 */
        .main-container {
            width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        /* 标题区域 */
        .hero-section {
            height: 600px;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 50%, #3b82f6 100%);
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            color: white;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            inset: 0;
            background: url('https://c8.alamy.com/comp/2R6M2N2/aerial-view-of-marina-bay-sands-and-singapore-city-harbour-at-night-singapore-southeast-asia-asia-2R6M2N2.jpg') center/cover;
            opacity: 0.3;
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 1000px;
            padding: 0 40px;
        }

        .hero-title {
            font-size: 4.5rem;
            font-weight: 900;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #ffffff, var(--accent-gold), #ffffff);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 3s ease-in-out infinite;
            line-height: 1.1;
        }

        .hero-subtitle {
            font-size: 2rem;
            font-weight: 300;
            margin-bottom: 40px;
            opacity: 0.95;
        }

        .hero-features {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 40px;
        }

        .hero-feature {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            padding: 20px 30px;
            border-radius: 50px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 1.3rem;
            font-weight: 600;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* 分隔线样式 */
        .section-divider {
            height: 8px;
            background: linear-gradient(90deg, transparent, var(--accent-gold), var(--dark-gold), var(--accent-gold), transparent);
            margin: 0;
        }

        /* 内容区块样式 */
        .content-section {
            padding: 80px 60px;
            position: relative;
        }

        .section-title {
            font-size: 3.5rem;
            font-weight: 900;
            text-align: center;
            margin-bottom: 60px;
            color: var(--primary-blue);
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 6px;
            background: linear-gradient(90deg, var(--accent-gold), var(--dark-gold));
            border-radius: 3px;
        }

        .section-description {
            font-size: 1.5rem;
            text-align: center;
            color: var(--text-light);
            margin-bottom: 80px;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.8;
        }

        /* 车型卡片样式 */
        .vehicle-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            border-radius: 30px;
            margin-bottom: 40px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            border: 2px solid rgba(245, 158, 11, 0.2);
            transition: all 0.3s ease;
            position: relative;
        }

        .vehicle-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, var(--accent-gold), var(--dark-gold), var(--secondary-blue));
        }

        .vehicle-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.25);
        }

        .vehicle-image {
            width: 100%;
            height: 300px;
            object-fit: cover;
            border-radius: 30px 30px 0 0;
        }

        .vehicle-content {
            padding: 50px;
        }

        .vehicle-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary-blue);
            margin-bottom: 30px;
            text-align: center;
        }

        .vehicle-specs {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            gap: 20px;
        }

        .spec-item {
            background: linear-gradient(135deg, #e0f2fe, #f0f9ff);
            padding: 20px 30px;
            border-radius: 20px;
            text-align: center;
            flex: 1;
            border: 2px solid rgba(59, 130, 246, 0.2);
        }

        .spec-icon {
            font-size: 2rem;
            color: var(--secondary-blue);
            margin-bottom: 10px;
        }

        .spec-text {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--secondary-blue);
        }

        .vehicle-description {
            font-size: 1.4rem;
            color: var(--text-light);
            text-align: center;
            line-height: 1.8;
        }

        /* 导出按钮 */
        .export-button {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, var(--accent-gold), var(--dark-gold));
            color: white;
            padding: 20px 30px;
            border-radius: 50px;
            border: none;
            font-size: 1.2rem;
            font-weight: 700;
            cursor: pointer;
            box-shadow: 0 10px 30px rgba(245, 158, 11, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .export-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(245, 158, 11, 0.6);
        }

        /* 响应式优化 */
        @media (max-width: 1200px) {
            body, .main-container {
                width: 100%;
                max-width: 1200px;
            }
        }

        /* 航站楼信息样式 */
        .terminal-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 60px;
        }

        .terminal-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(59, 130, 246, 0.2);
            transition: all 0.3s ease;
        }

        .terminal-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
        }

        .terminal-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
        }

        .terminal-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--secondary-blue), var(--primary-blue));
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: 900;
            margin-right: 20px;
            box-shadow: 0 10px 25px rgba(30, 64, 175, 0.3);
        }

        .terminal-title {
            font-size: 2rem;
            font-weight: 800;
            color: var(--primary-blue);
            margin: 0;
        }

        .pickup-location {
            display: flex;
            align-items: center;
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--secondary-blue);
            margin-bottom: 20px;
            background: linear-gradient(135deg, #e0f2fe, #f0f9ff);
            padding: 15px 20px;
            border-radius: 15px;
            border: 2px solid rgba(59, 130, 246, 0.2);
        }

        .pickup-location i {
            font-size: 1.5rem;
            margin-right: 15px;
            color: var(--accent-gold);
        }

        .terminal-steps {
            margin-top: 20px;
        }

        .step-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 1.1rem;
            color: var(--text-dark);
        }

        .step-number {
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, var(--accent-gold), var(--dark-gold));
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            margin-right: 15px;
            font-size: 0.9rem;
        }

        .terminal-description {
            font-size: 1.1rem;
            color: var(--text-light);
            line-height: 1.6;
            margin-top: 15px;
        }

        /* 服务特色样式 */
        .service-features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 60px;
        }

        .feature-item {
            text-align: center;
            padding: 50px 30px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            border-radius: 25px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(245, 158, 11, 0.2);
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
        }

        .feature-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, var(--accent-gold), var(--dark-gold));
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            margin: 0 auto 30px;
            box-shadow: 0 15px 35px rgba(245, 158, 11, 0.4);
        }

        .feature-title {
            font-size: 2rem;
            font-weight: 800;
            color: var(--primary-blue);
            margin-bottom: 20px;
        }

        .feature-description {
            font-size: 1.2rem;
            color: var(--text-light);
            line-height: 1.7;
        }

        /* 联系方式样式 */
        .contact-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 60px;
        }

        .contact-card {
            text-align: center;
            padding: 40px 30px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            border-radius: 25px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(34, 197, 94, 0.2);
            transition: all 0.3s ease;
        }

        .contact-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
            border-color: rgba(34, 197, 94, 0.4);
        }

        .contact-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.2rem;
            margin: 0 auto 25px;
            box-shadow: 0 15px 35px rgba(34, 197, 94, 0.4);
        }

        .contact-title {
            font-size: 1.8rem;
            font-weight: 800;
            color: var(--primary-blue);
            margin-bottom: 15px;
        }

        .contact-detail {
            font-size: 1.5rem;
            font-weight: 700;
            color: #22c55e;
            margin-bottom: 10px;
        }

        .contact-description {
            font-size: 1.1rem;
            color: var(--text-light);
        }

        /* 重要提醒样式 */
        .important-notice {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            border-radius: 25px;
            padding: 40px;
            border: 3px solid #f59e0b;
            margin-top: 60px;
        }

        .notice-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }

        .notice-header i {
            font-size: 2.5rem;
            color: #d97706;
            margin-right: 20px;
        }

        .notice-header h3 {
            font-size: 2.2rem;
            font-weight: 800;
            color: #92400e;
            margin: 0;
        }

        .notice-content ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .notice-content li {
            font-size: 1.3rem;
            color: #92400e;
            margin-bottom: 15px;
            padding-left: 30px;
            position: relative;
        }

        .notice-content li::before {
            content: '⚠️';
            position: absolute;
            left: 0;
            top: 0;
            font-size: 1.2rem;
        }

        /* 页脚样式 */
        .footer-section {
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            color: white;
            padding: 60px 60px 40px;
            text-align: center;
        }

        .footer-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .footer-logo h2 {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #ffffff, var(--accent-gold));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .footer-logo p {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .footer-info p {
            font-size: 1.1rem;
            opacity: 0.8;
            margin-bottom: 10px;
        }

        /* 打印和导出优化 */
        @media print {
            .export-button {
                display: none !important;
            }

            body {
                background: white;
                box-shadow: none;
            }

            .main-container {
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 主标题区域 -->
        <section class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">新加坡专业接送机服务</h1>
                <p class="hero-subtitle">新加坡旅行一站式服务 · 品质之选 · 安全保障</p>
                <div class="hero-features">
                    <div class="hero-feature">
                        <i class="fas fa-star mr-3"></i>专业品质
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-shield-alt mr-3"></i>安全保障
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-clock mr-3"></i>准时可靠
                    </div>
                </div>
            </div>
        </section>

        <!-- 分隔线 -->
        <div class="section-divider"></div>

        <!-- 车型选择区域 -->
        <section class="content-section">
            <h2 class="section-title">精选车型服务</h2>
            <p class="section-description">
                根据您的需求和人数，我们提供多种车型选择，从经济型轿车到豪华MPV，满足不同的接送需求。每款车型都经过精心维护，确保您的出行安全舒适。
            </p>

            <!-- 标准轿车 -->
            <div class="vehicle-card">
                <img src="https://toyota-cms-media.s3.amazonaws.com/wp-content/uploads/2022/09/2023-toyota-rav4-hybrid-xse-interior-001.jpg" alt="标准轿车内饰" class="vehicle-image">
                <div class="vehicle-content">
                    <h3 class="vehicle-title">标准轿车</h3>
                    <div class="vehicle-specs">
                        <div class="spec-item">
                            <div class="spec-icon"><i class="fas fa-user"></i></div>
                            <div class="spec-text">4人</div>
                        </div>
                        <div class="spec-item">
                            <div class="spec-icon"><i class="fas fa-suitcase"></i></div>
                            <div class="spec-text">3大2中行李</div>
                        </div>
                        <div class="spec-item">
                            <div class="spec-icon"><i class="fas fa-dollar-sign"></i></div>
                            <div class="spec-text">经济实惠</div>
                        </div>
                    </div>
                    <p class="vehicle-description">
                        经济实惠的选择，适合小型家庭或商务出行，提供舒适的乘坐体验。配备现代化设施，确保您的旅程安全舒适。
                    </p>
                </div>
            </div>

            <!-- 豪华轿车 -->
            <div class="vehicle-card">
                <img src="https://www.mbusa.com/content/dam/mb-nafta/us/myco/my25/e-class/e-sedan/gallery/series/gallery-class/2025-E-SEDAN-GAL-026-Q-WP.jpg" alt="豪华轿车内饰" class="vehicle-image">
                <div class="vehicle-content">
                    <h3 class="vehicle-title">豪华轿车 (奔驰E级同级)</h3>
                    <div class="vehicle-specs">
                        <div class="spec-item">
                            <div class="spec-icon"><i class="fas fa-user"></i></div>
                            <div class="spec-text">4人</div>
                        </div>
                        <div class="spec-item">
                            <div class="spec-icon"><i class="fas fa-suitcase"></i></div>
                            <div class="spec-text">3大3中行李</div>
                        </div>
                        <div class="spec-item">
                            <div class="spec-icon"><i class="fas fa-crown"></i></div>
                            <div class="spec-text">豪华体验</div>
                        </div>
                    </div>
                    <p class="vehicle-description">
                        高级商务之选，配备真皮座椅和豪华内饰，为您提供尊贵舒适的乘坐体验。适合重要商务场合和特殊时刻。
                    </p>
                </div>
            </div>

            <!-- 商务MPV -->
            <div class="vehicle-card">
                <img src="https://www.mundilimos.com/wp-content/uploads/2021/06/Sprinter-Van-Interior-05.jpg" alt="商务MPV内饰" class="vehicle-image">
                <div class="vehicle-content">
                    <h3 class="vehicle-title">商务MPV (丰田 Voxy)</h3>
                    <div class="vehicle-specs">
                        <div class="spec-item">
                            <div class="spec-icon"><i class="fas fa-user"></i></div>
                            <div class="spec-text">4人</div>
                        </div>
                        <div class="spec-item">
                            <div class="spec-icon"><i class="fas fa-suitcase"></i></div>
                            <div class="spec-text">4大4中行李</div>
                        </div>
                        <div class="spec-item">
                            <div class="spec-icon"><i class="fas fa-expand"></i></div>
                            <div class="spec-text">宽敞空间</div>
                        </div>
                    </div>
                    <p class="vehicle-description">
                        家人或朋友出行首选，宽敞舒适，拥有充足的乘坐空间和行李存放空间。适合多人团体和长途旅行。
                    </p>
                </div>
            </div>

            <!-- 丰田阿尔法 -->
            <div class="vehicle-card">
                <img src="https://www.batfa.com/photo-newcar-alphardhybrid-interior.files/Alphard-interior-Flaxen-8.jpg" alt="丰田阿尔法内饰" class="vehicle-image">
                <div class="vehicle-content">
                    <h3 class="vehicle-title">丰田阿尔法/威尔法</h3>
                    <div class="vehicle-specs">
                        <div class="spec-item">
                            <div class="spec-icon"><i class="fas fa-user"></i></div>
                            <div class="spec-text">5人</div>
                        </div>
                        <div class="spec-item">
                            <div class="spec-icon"><i class="fas fa-suitcase"></i></div>
                            <div class="spec-text">5大3中行李</div>
                        </div>
                        <div class="spec-item">
                            <div class="spec-icon"><i class="fas fa-gem"></i></div>
                            <div class="spec-text">旗舰级</div>
                        </div>
                    </div>
                    <p class="vehicle-description">
                        旗舰级豪华MPV，大空间配备顶级内饰，为团体旅行提供极致舒适享受。商务接待和家庭出行的完美选择。
                    </p>
                </div>
            </div>
        </section>

        <!-- 分隔线 -->
        <div class="section-divider"></div>

        <!-- 航站楼指引区域 -->
        <section class="content-section">
            <h2 class="section-title">樟宜机场接机指引</h2>
            <p class="section-description">
                新加坡樟宜机场各航站楼指定上车点位置，我们的专业司机将在此处等候接机，确保您快速便捷地开始新加坡之旅。
            </p>

            <!-- 航站楼信息卡片 -->
            <div class="terminal-info-grid">
                <div class="terminal-card">
                    <div class="terminal-header">
                        <div class="terminal-icon">T1</div>
                        <h3 class="terminal-title">T1航站楼</h3>
                    </div>
                    <div class="terminal-content">
                        <div class="pickup-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>10-12号门 (地下一层B1)</span>
                        </div>
                        <div class="terminal-steps">
                            <div class="step-item">
                                <span class="step-number">1</span>
                                <span>寻找"Ride-Hailing & Arrival Pick-Up"指示牌</span>
                            </div>
                            <div class="step-item">
                                <span class="step-number">2</span>
                                <span>乘坐下行扶梯至地下一层</span>
                            </div>
                            <div class="step-item">
                                <span class="step-number">3</span>
                                <span>前往10-12号门等候司机</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="terminal-card">
                    <div class="terminal-header">
                        <div class="terminal-icon">T2</div>
                        <h3 class="terminal-title">T2航站楼</h3>
                    </div>
                    <div class="terminal-content">
                        <div class="pickup-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>3号门 (航站楼中央区域)</span>
                        </div>
                        <div class="terminal-description">
                            抵达迎宾大厅中央，靠近咨询柜台，然后前往3号门指定上车区域等候司机。
                        </div>
                    </div>
                </div>

                <div class="terminal-card">
                    <div class="terminal-header">
                        <div class="terminal-icon">T3</div>
                        <h3 class="terminal-title">T3航站楼</h3>
                    </div>
                    <div class="terminal-content">
                        <div class="pickup-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>1-2号门 (地下一层B1)</span>
                        </div>
                        <div class="terminal-description">
                            按照"Ride-Hailing & Arrival Pick-Up"指示牌，乘坐扶梯下至地下一层，在1-2号门等候。
                        </div>
                    </div>
                </div>

                <div class="terminal-card">
                    <div class="terminal-header">
                        <div class="terminal-icon">T4</div>
                        <h3 class="terminal-title">T4航站楼</h3>
                    </div>
                    <div class="terminal-content">
                        <div class="pickup-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>5-7号门</span>
                        </div>
                        <div class="terminal-description">
                            从到达大厅按照"Ride-Hailing Pick-up"标识，前往5-7号门等候司机接送。
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 分隔线 -->
        <div class="section-divider"></div>

        <!-- 服务特色区域 -->
        <section class="content-section">
            <h2 class="section-title">专业服务保障</h2>
            <p class="section-description">
                我们致力于为每一位客户提供安全、舒适、准时的接送服务，让您的新加坡之旅从踏出机场的那一刻就开始享受。
            </p>

            <div class="service-features">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">安全保障</h3>
                    <p class="feature-description">
                        所有车辆定期保养检查，司机经过专业培训，持有合法营运执照，为您的出行安全保驾护航。
                    </p>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="feature-title">准时可靠</h3>
                    <p class="feature-description">
                        实时航班监控，提前安排司机到位，确保您下飞机后能够第一时间见到我们的专业司机。
                    </p>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3 class="feature-title">专业品质</h3>
                    <p class="feature-description">
                        多年新加坡接送服务经验，熟悉当地路况，提供最优路线规划，让您的旅程更加顺畅。
                    </p>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3 class="feature-title">贴心服务</h3>
                    <p class="feature-description">
                        24小时客服支持，中文沟通无障碍，随时为您解答疑问，提供旅行建议和帮助。
                    </p>
                </div>
            </div>
        </section>

        <!-- 分隔线 -->
        <div class="section-divider"></div>

        <!-- 联系方式区域 -->
        <section class="content-section">
            <h2 class="section-title">立即预订服务</h2>
            <p class="section-description">
                专业团队随时为您服务，提供24小时预订和咨询支持，让您的新加坡之旅从预订开始就享受贴心服务。
            </p>

            <div class="contact-info">
                <div class="contact-card">
                    <div class="contact-icon">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <h3 class="contact-title">WhatsApp预订</h3>
                    <p class="contact-detail">+65 8888 8888</p>
                    <p class="contact-description">24小时在线，即时回复</p>
                </div>

                <div class="contact-card">
                    <div class="contact-icon">
                        <i class="fab fa-weixin"></i>
                    </div>
                    <h3 class="contact-title">微信咨询</h3>
                    <p class="contact-detail">SGTransport2024</p>
                    <p class="contact-description">中文服务，专业解答</p>
                </div>

                <div class="contact-card">
                    <div class="contact-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <h3 class="contact-title">电话预订</h3>
                    <p class="contact-detail">+65 6888 8888</p>
                    <p class="contact-description">工作时间：8:00-22:00</p>
                </div>

                <div class="contact-card">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h3 class="contact-title">邮件咨询</h3>
                    <p class="contact-detail"><EMAIL></p>
                    <p class="contact-description">详细行程规划</p>
                </div>
            </div>

            <!-- 重要提醒 -->
            <div class="important-notice">
                <div class="notice-header">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>重要提醒</h3>
                </div>
                <div class="notice-content">
                    <ul>
                        <li><strong>提前预订：</strong>建议至少提前24小时预订，确保车辆安排</li>
                        <li><strong>航班信息：</strong>请提供准确的航班号和到达时间</li>
                        <li><strong>联系方式：</strong>请保持手机畅通，司机会提前联系</li>
                        <li><strong>入境须知：</strong>中国护照免签30天，需填写电子入境卡</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 页脚信息 -->
        <footer class="footer-section">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>新加坡专业接送机服务</h2>
                    <p>您的新加坡旅行伙伴</p>
                </div>
                <div class="footer-info">
                    <p>© 2024 新加坡专业接送机服务 | 安全 · 专业 · 可靠</p>
                    <p>营业执照：XXXXXXXXX | 服务热线：+65 6888 8888</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- 导出按钮 -->
    <button class="export-button" id="exportButton">
        <i class="fas fa-download mr-3"></i>
        导出产品图
    </button>

    <script>
        // 导出功能
        document.getElementById('exportButton').addEventListener('click', function() {
            // 隐藏导出按钮
            this.style.display = 'none';

            // 配置html2canvas选项
            const options = {
                useCORS: true,
                allowTaint: true,
                scale: 2, // 提高图片质量
                width: 1200,
                backgroundColor: '#ffffff',
                logging: false,
                onclone: function(clonedDoc) {
                    // 在克隆的文档中隐藏导出按钮
                    const clonedButton = clonedDoc.getElementById('exportButton');
                    if (clonedButton) {
                        clonedButton.style.display = 'none';
                    }
                }
            };

            // 使用html2canvas生成图片
            html2canvas(document.body, options).then(function(canvas) {
                // 创建下载链接
                const link = document.createElement('a');
                link.download = '新加坡接送机服务-产品宣传图.png';
                link.href = canvas.toDataURL('image/png', 1.0);

                // 触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 显示成功消息
                alert('产品图导出成功！');

                // 重新显示导出按钮
                document.getElementById('exportButton').style.display = 'flex';
            }).catch(function(error) {
                console.error('导出失败:', error);
                alert('导出失败，请重试');

                // 重新显示导出按钮
                document.getElementById('exportButton').style.display = 'flex';
            });
        });

        // 页面加载完成后的优化
        window.addEventListener('load', function() {
            // 预加载所有图片
            const images = document.querySelectorAll('img');
            let loadedImages = 0;

            images.forEach(function(img) {
                if (img.complete) {
                    loadedImages++;
                } else {
                    img.addEventListener('load', function() {
                        loadedImages++;
                        if (loadedImages === images.length) {
                            console.log('所有图片加载完成，可以导出');
                        }
                    });
                }
            });

            // 添加平滑滚动
            document.documentElement.style.scrollBehavior = 'smooth';

            // 添加页面加载完成提示
            console.log('新加坡接送机服务产品图页面加载完成');
        });

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有卡片添加点击效果
            const cards = document.querySelectorAll('.vehicle-card, .terminal-card, .feature-item, .contact-card');
            cards.forEach(function(card) {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>